# Dependencies
node_modules
*/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
*/dist
*/build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Git
.git
.gitignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Temporary folders
tmp
temp

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentation
README.md
docs/

# Tests
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
test/
tests/
__tests__/
