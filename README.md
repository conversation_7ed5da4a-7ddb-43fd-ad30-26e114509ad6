# E-commerce Platform

一个使用现代技术栈构建的完整电商平台，包含用户认证、商品管理、购物车和订单系统。

## 🚀 技术栈

### 后端
- **框架**: <PERSON><PERSON> (轻量级 Web 框架)
- **运行时**: <PERSON><PERSON> (高性能 JavaScript 运行时)
- **数据库**: PostgreSQL
- **认证**: JWT (JSON Web Tokens)
- **验证**: Zod
- **密码加密**: bcryptjs

### 前端
- **框架**: React 18
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **HTTP 客户端**: Axios
- **路由**: React Router
- **表单**: React Hook Form

### 部署
- **容器化**: Docker & Docker Compose
- **Web 服务器**: Nginx (前端)
- **缓存**: Redis (可选)

## 📁 项目结构

```
ecommerce-platform/
├── backend/                 # 后端 API
│   ├── src/
│   │   ├── db/             # 数据库配置和迁移
│   │   ├── routes/         # API 路由
│   │   ├── middleware/     # 中间件
│   │   ├── models/         # 数据模型
│   │   └── utils/          # 工具函数
│   ├── Dockerfile
│   └── package.json
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── store/          # 状态管理
│   │   ├── services/       # API 服务
│   │   └── types/          # TypeScript 类型
│   ├── Dockerfile
│   └── package.json
├── docker-compose.yml      # 生产环境配置
├── docker-compose.dev.yml  # 开发环境配置
└── README.md
```

## 🛠️ 快速开始

### 前置要求

- [Bun](https://bun.sh/) >= 1.0.0
- [Docker](https://www.docker.com/) >= 20.0.0
- [Docker Compose](https://docs.docker.com/compose/) >= 2.0.0

### 1. 克隆项目

```bash
git clone <repository-url>
cd ecommerce-platform
```

### 2. 使用 Docker 运行（推荐）

#### 开发环境

```bash
# 启动开发环境数据库
docker-compose -f docker-compose.dev.yml up -d

# 安装依赖
bun install

# 运行数据库迁移
bun run db:migrate

# 添加种子数据
bun run db:seed

# 启动开发服务器
bun run dev
```

#### 生产环境

```bash
# 构建并启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 本地开发

#### 后端设置

```bash
cd backend

# 安装依赖
bun install

# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接
# 运行数据库迁移
bun run db:migrate

# 添加种子数据
bun run db:seed

# 启动开发服务器
bun run dev
```

#### 前端设置

```bash
cd frontend

# 安装依赖
bun install

# 启动开发服务器
bun run dev
```

## 🌐 访问应用

- **前端**: http://localhost:3000
- **后端 API**: http://localhost:3001
- **数据库管理** (开发环境): http://localhost:5050
  - 用户名: <EMAIL>
  - 密码: admin

## 📊 默认账户

种子数据包含以下测试账户：

### 管理员账户
- **邮箱**: <EMAIL>
- **密码**: admin123

### 普通用户账户
- **邮箱**: <EMAIL>
- **密码**: user123

## 🔧 可用脚本

### 根目录
```bash
bun run dev              # 同时启动前后端开发服务器
bun run build            # 构建前后端应用
bun run start            # 启动生产服务器
bun run db:migrate       # 运行数据库迁移
bun run db:seed          # 添加种子数据
bun run docker:up        # 启动 Docker 服务
bun run docker:down      # 停止 Docker 服务
bun run test             # 运行测试
```

### 后端
```bash
bun run dev              # 启动开发服务器
bun run build            # 构建应用
bun run start            # 启动生产服务器
bun run db:migrate       # 运行数据库迁移
bun run db:seed          # 添加种子数据
bun run test             # 运行测试
```

### 前端
```bash
bun run dev              # 启动开发服务器
bun run build            # 构建应用
bun run preview          # 预览构建结果
bun run test             # 运行测试
```

## 🗄️ 数据库结构

### 主要表

- **users**: 用户信息
- **categories**: 商品分类
- **products**: 商品信息
- **cart_items**: 购物车项目
- **orders**: 订单信息
- **order_items**: 订单项目
- **addresses**: 用户地址

### 关系

- 用户可以有多个地址
- 用户可以有多个购物车项目
- 用户可以有多个订单
- 商品属于一个分类
- 订单包含多个订单项目

## 🔐 API 端点

### 认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `PUT /api/auth/me` - 更新用户信息

### 商品
- `GET /api/products` - 获取商品列表
- `GET /api/products/:id` - 获取单个商品
- `POST /api/products` - 创建商品 (管理员)
- `PUT /api/products/:id` - 更新商品 (管理员)
- `DELETE /api/products/:id` - 删除商品 (管理员)

### 购物车
- `GET /api/cart` - 获取购物车
- `POST /api/cart/items` - 添加商品到购物车
- `PUT /api/cart/items/:id` - 更新购物车项目
- `DELETE /api/cart/items/:id` - 删除购物车项目
- `DELETE /api/cart` - 清空购物车

## 🚀 部署

### Docker 部署

1. 确保已安装 Docker 和 Docker Compose
2. 克隆项目到服务器
3. 配置环境变量
4. 运行部署命令

```bash
# 生产环境部署
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 环境变量

在生产环境中，请确保设置以下环境变量：

```bash
# 数据库
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ecommerce
DB_USER=postgres
DB_PASSWORD=your-secure-password

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 应用
NODE_ENV=production
FRONTEND_URL=https://your-domain.com
```

## 🧪 测试

```bash
# 运行所有测试
bun run test

# 运行后端测试
bun run test:backend

# 运行前端测试
bun run test:frontend
```

## 📝 开发指南

### 添加新功能

1. 后端：在 `backend/src/routes/` 中添加新路由
2. 前端：在 `frontend/src/pages/` 或 `frontend/src/components/` 中添加新组件
3. 更新类型定义
4. 添加测试用例

### 数据库迁移

```bash
# 创建新迁移文件
# 在 backend/src/db/migrations/ 中创建新的 SQL 文件

# 运行迁移
bun run db:migrate
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 是否正在运行
   - 验证数据库连接配置

2. **端口冲突**
   - 确保端口 3000, 3001, 5432 未被占用
   - 或在配置文件中修改端口

3. **Docker 构建失败**
   - 清理 Docker 缓存：`docker system prune -a`
   - 重新构建：`docker-compose build --no-cache`

### 获取帮助

如果遇到问题，请：
1. 查看日志文件
2. 检查环境变量配置
3. 确认所有依赖已正确安装
4. 在 GitHub Issues 中报告问题
