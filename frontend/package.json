{"name": "ecommerce-frontend", "version": "1.0.0", "description": "E-commerce platform frontend built with React", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@tanstack/react-query": "^5.8.0", "axios": "^1.6.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.20.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.1.0", "vite": "^5.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "vitest": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jsdom": "^23.0.0"}}