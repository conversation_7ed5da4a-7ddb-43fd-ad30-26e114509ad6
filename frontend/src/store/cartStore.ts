import { create } from 'zustand';
import { CartItem } from '@/types';
import { apiService } from '@/services/api';

interface CartState {
  items: CartItem[];
  totalAmount: number;
  itemCount: number;
  isLoading: boolean;
  fetchCart: () => Promise<void>;
  addToCart: (productId: number, quantity: number) => Promise<void>;
  updateCartItem: (itemId: number, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
}

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  totalAmount: 0,
  itemCount: 0,
  isLoading: false,

  fetchCart: async () => {
    try {
      set({ isLoading: true });
      const response = await apiService.getCart();
      
      if (response.success && response.data) {
        set({
          items: response.data.items,
          totalAmount: response.data.total_amount,
          itemCount: response.data.item_count,
        });
      }
    } catch (error) {
      console.error('Failed to fetch cart:', error);
      // If user is not authenticated, clear cart
      set({ items: [], totalAmount: 0, itemCount: 0 });
    } finally {
      set({ isLoading: false });
    }
  },

  addToCart: async (productId: number, quantity: number) => {
    try {
      set({ isLoading: true });
      const response = await apiService.addToCart({ product_id: productId, quantity });
      
      if (response.success) {
        // Refresh cart after adding item
        await get().fetchCart();
      }
    } catch (error) {
      console.error('Failed to add to cart:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  updateCartItem: async (itemId: number, quantity: number) => {
    try {
      set({ isLoading: true });
      const response = await apiService.updateCartItem(itemId, quantity);
      
      if (response.success) {
        // Update local state
        const { items } = get();
        const updatedItems = items.map(item => 
          item.id === itemId 
            ? { ...item, quantity, total_price: item.product_price * quantity }
            : item
        );
        
        const totalAmount = updatedItems.reduce((sum, item) => sum + item.total_price, 0);
        
        set({
          items: updatedItems,
          totalAmount,
          itemCount: updatedItems.length,
        });
      }
    } catch (error) {
      console.error('Failed to update cart item:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  removeFromCart: async (itemId: number) => {
    try {
      set({ isLoading: true });
      const response = await apiService.removeFromCart(itemId);
      
      if (response.success) {
        // Update local state
        const { items } = get();
        const updatedItems = items.filter(item => item.id !== itemId);
        const totalAmount = updatedItems.reduce((sum, item) => sum + item.total_price, 0);
        
        set({
          items: updatedItems,
          totalAmount,
          itemCount: updatedItems.length,
        });
      }
    } catch (error) {
      console.error('Failed to remove from cart:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  clearCart: async () => {
    try {
      set({ isLoading: true });
      const response = await apiService.clearCart();
      
      if (response.success) {
        set({
          items: [],
          totalAmount: 0,
          itemCount: 0,
        });
      }
    } catch (error) {
      console.error('Failed to clear cart:', error);
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },
}));
