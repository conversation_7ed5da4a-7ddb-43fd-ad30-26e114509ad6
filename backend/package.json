{"name": "ecommerce-backend", "version": "1.0.0", "description": "E-commerce platform backend API built with Hono", "main": "src/index.ts", "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target node", "start": "bun run dist/index.js", "db:migrate": "bun run src/db/migrate.ts", "db:seed": "bun run src/db/seed.ts", "test": "bun test"}, "dependencies": {"hono": "^3.12.0", "@hono/node-server": "^1.8.0", "pg": "^8.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.0", "cors": "^2.8.5", "dotenv": "^16.3.0"}, "devDependencies": {"@types/pg": "^8.10.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "typescript": "^5.0.0", "bun-types": "latest"}, "peerDependencies": {"typescript": "^5.0.0"}}