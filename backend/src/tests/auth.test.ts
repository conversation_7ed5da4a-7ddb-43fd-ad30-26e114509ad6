import { describe, test, expect, beforeAll, afterAll } from 'bun:test';
import { Hono } from 'hono';
import authRoutes from '../routes/auth';
import { pool } from '../db/config';

const app = new Hono();
app.route('/auth', authRoutes);

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'Test',
  last_name: 'User',
  phone: '+1234567890'
};

describe('Auth Routes', () => {
  beforeAll(async () => {
    // Clean up test user if exists
    await pool.query('DELETE FROM users WHERE email = $1', [testUser.email]);
  });

  afterAll(async () => {
    // Clean up test user
    await pool.query('DELETE FROM users WHERE email = $1', [testUser.email]);
  });

  test('POST /auth/register - should register a new user', async () => {
    const req = new Request('http://localhost/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testUser),
    });

    const res = await app.fetch(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.user.email).toBe(testUser.email);
    expect(data.data.token).toBeDefined();
  });

  test('POST /auth/register - should not register user with existing email', async () => {
    const req = new Request('http://localhost/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testUser),
    });

    const res = await app.fetch(req);
    const data = await res.json();

    expect(res.status).toBe(400);
    expect(data.success).toBe(false);
    expect(data.error).toContain('already exists');
  });

  test('POST /auth/login - should login with valid credentials', async () => {
    const req = new Request('http://localhost/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    });

    const res = await app.fetch(req);
    const data = await res.json();

    expect(res.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.user.email).toBe(testUser.email);
    expect(data.data.token).toBeDefined();
  });

  test('POST /auth/login - should not login with invalid credentials', async () => {
    const req = new Request('http://localhost/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: 'wrongpassword',
      }),
    });

    const res = await app.fetch(req);
    const data = await res.json();

    expect(res.status).toBe(401);
    expect(data.success).toBe(false);
    expect(data.error).toContain('Invalid email or password');
  });

  test('POST /auth/register - should validate required fields', async () => {
    const req = new Request('http://localhost/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: 'invalid-email',
        password: '123', // too short
        first_name: '',
      }),
    });

    const res = await app.fetch(req);
    const data = await res.json();

    expect(res.status).toBe(400);
    expect(data.success).toBe(false);
  });
});
