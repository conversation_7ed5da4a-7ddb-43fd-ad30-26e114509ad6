// User types
export interface User {
  id: number;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  phone?: string;
  is_admin: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

// Category types
export interface Category {
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  parent_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  image_url?: string;
  parent_id?: number;
}

// Product types
export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  stock_quantity: number;
  category_id?: number;
  image_url?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface ProductWithCategory extends Product {
  category_name?: string;
}

export interface CreateProductData {
  name: string;
  description?: string;
  price: number;
  stock_quantity: number;
  category_id?: number;
  image_url?: string;
}

export interface UpdateProductData {
  name?: string;
  description?: string;
  price?: number;
  stock_quantity?: number;
  category_id?: number;
  image_url?: string;
  is_active?: boolean;
}

// Address types
export interface Address {
  id: number;
  user_id: number;
  type: 'billing' | 'shipping';
  first_name: string;
  last_name: string;
  company?: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CreateAddressData {
  type: 'billing' | 'shipping';
  first_name: string;
  last_name: string;
  company?: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default?: boolean;
}

// Order types
export interface Order {
  id: number;
  user_id: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total_amount: number;
  shipping_address_id?: number;
  billing_address_id?: number;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method?: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: Date;
}

export interface OrderWithItems extends Order {
  items: (OrderItem & { product_name: string })[];
  shipping_address?: Address;
  billing_address?: Address;
}

export interface CreateOrderData {
  items: {
    product_id: number;
    quantity: number;
  }[];
  shipping_address_id?: number;
  billing_address_id?: number;
  payment_method?: string;
  notes?: string;
}

// Cart types
export interface CartItem {
  id: number;
  user_id: number;
  product_id: number;
  quantity: number;
  created_at: Date;
  updated_at: Date;
}

export interface CartItemWithProduct extends CartItem {
  product_name: string;
  product_price: number;
  product_image_url?: string;
  total_price: number;
}

export interface AddToCartData {
  product_id: number;
  quantity: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// JWT Payload
export interface JWTPayload {
  userId: number;
  email: string;
  isAdmin: boolean;
}
