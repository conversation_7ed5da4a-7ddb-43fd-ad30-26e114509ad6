import bcrypt from 'bcryptjs';
import { pool, testConnection, closeConnection } from './config';

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');
  
  // Test connection first
  const isConnected = await testConnection();
  if (!isConnected) {
    console.error('❌ Cannot connect to database. Please check your configuration.');
    process.exit(1);
  }

  try {
    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10);
    await pool.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, is_admin)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (email) DO NOTHING
    `, ['<EMAIL>', adminPassword, 'Admin', 'User', true]);

    // Create test user
    const userPassword = await bcrypt.hash('user123', 10);
    await pool.query(`
      INSERT INTO users (email, password_hash, first_name, last_name, phone)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (email) DO NOTHING
    `, ['<EMAIL>', userPassword, 'Test', 'User', '+1234567890']);

    console.log('✅ Users created');

    // Create categories
    const categories = [
      { name: 'Electronics', description: 'Electronic devices and gadgets' },
      { name: 'Clothing', description: 'Fashion and apparel' },
      { name: 'Books', description: 'Books and literature' },
      { name: 'Home & Garden', description: 'Home improvement and gardening' },
      { name: 'Sports', description: 'Sports and outdoor equipment' }
    ];

    for (const category of categories) {
      await pool.query(`
        INSERT INTO categories (name, description)
        VALUES ($1, $2)
        ON CONFLICT DO NOTHING
      `, [category.name, category.description]);
    }

    console.log('✅ Categories created');

    // Create sample products
    const products = [
      {
        name: 'Smartphone Pro Max',
        description: 'Latest flagship smartphone with advanced features',
        price: 999.99,
        stock_quantity: 50,
        category: 'Electronics'
      },
      {
        name: 'Wireless Headphones',
        description: 'Premium noise-cancelling wireless headphones',
        price: 299.99,
        stock_quantity: 100,
        category: 'Electronics'
      },
      {
        name: 'Cotton T-Shirt',
        description: 'Comfortable 100% cotton t-shirt',
        price: 29.99,
        stock_quantity: 200,
        category: 'Clothing'
      },
      {
        name: 'Programming Book',
        description: 'Learn modern web development',
        price: 49.99,
        stock_quantity: 75,
        category: 'Books'
      },
      {
        name: 'Garden Tools Set',
        description: 'Complete set of essential garden tools',
        price: 89.99,
        stock_quantity: 30,
        category: 'Home & Garden'
      },
      {
        name: 'Running Shoes',
        description: 'Professional running shoes for athletes',
        price: 129.99,
        stock_quantity: 80,
        category: 'Sports'
      }
    ];

    for (const product of products) {
      // Get category ID
      const categoryResult = await pool.query(
        'SELECT id FROM categories WHERE name = $1',
        [product.category]
      );
      
      if (categoryResult.rows.length > 0) {
        const categoryId = categoryResult.rows[0].id;
        
        await pool.query(`
          INSERT INTO products (name, description, price, stock_quantity, category_id)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT DO NOTHING
        `, [product.name, product.description, product.price, product.stock_quantity, categoryId]);
      }
    }

    console.log('✅ Products created');

    console.log('🎉 Database seeding completed successfully!');
    console.log('📧 Admin login: <EMAIL> / admin123');
    console.log('📧 User login: <EMAIL> / user123');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await closeConnection();
  }
}

// Run seeding if this file is executed directly
if (import.meta.main) {
  seedDatabase();
}

export { seedDatabase };
