import { readFileSync } from 'fs';
import { join } from 'path';
import { pool, testConnection, closeConnection } from './config';

async function runMigrations() {
  console.log('🚀 Starting database migrations...');
  
  // Test connection first
  const isConnected = await testConnection();
  if (!isConnected) {
    console.error('❌ Cannot connect to database. Please check your configuration.');
    process.exit(1);
  }

  try {
    // Create migrations table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Read and execute migration files
    const migrationFiles = [
      '001_create_tables.sql'
    ];

    for (const filename of migrationFiles) {
      // Check if migration has already been executed
      const result = await pool.query(
        'SELECT id FROM migrations WHERE filename = $1',
        [filename]
      );

      if (result.rows.length > 0) {
        console.log(`⏭️  Skipping ${filename} (already executed)`);
        continue;
      }

      console.log(`📄 Executing migration: ${filename}`);
      
      // Read migration file
      const migrationPath = join(__dirname, 'migrations', filename);
      const migrationSQL = readFileSync(migrationPath, 'utf8');
      
      // Execute migration
      await pool.query(migrationSQL);
      
      // Record migration as executed
      await pool.query(
        'INSERT INTO migrations (filename) VALUES ($1)',
        [filename]
      );
      
      console.log(`✅ Migration ${filename} executed successfully`);
    }

    console.log('🎉 All migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await closeConnection();
  }
}

// Run migrations if this file is executed directly
if (import.meta.main) {
  runMigrations();
}

export { runMigrations };
