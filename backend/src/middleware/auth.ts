import { Context, Next } from 'hono';
import { verifyToken, extractTokenFromHeader } from '../utils/jwt';
import { JWTPayload } from '../models/types';

// Extend Context type to include user
declare module 'hono' {
  interface Context {
    user?: JWTPayload;
  }
}

export async function authMiddleware(c: Context, next: Next) {
  try {
    const authHeader = c.req.header('Authorization');
    const token = extractTokenFromHeader(authHeader);
    const payload = verifyToken(token);
    
    c.user = payload;
    await next();
  } catch (error) {
    return c.json({ 
      success: false, 
      error: 'Unauthorized' 
    }, 401);
  }
}

export async function adminMiddleware(c: Context, next: Next) {
  if (!c.user?.isAdmin) {
    return c.json({ 
      success: false, 
      error: 'Admin access required' 
    }, 403);
  }
  await next();
}

export async function optionalAuthMiddleware(c: Context, next: Next) {
  try {
    const authHeader = c.req.header('Authorization');
    if (authHeader) {
      const token = extractTokenFromHeader(authHeader);
      const payload = verifyToken(token);
      c.user = payload;
    }
  } catch (error) {
    // Ignore auth errors for optional auth
  }
  await next();
}
