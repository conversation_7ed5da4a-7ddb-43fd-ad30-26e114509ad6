import { Hono } from 'hono';
import { pool } from '../db/config';
import { authMiddleware, adminMiddleware, optionalAuthMiddleware } from '../middleware/auth';
import { createProductSchema, updateProductSchema, productQuerySchema } from '../utils/validation';
import { Product, CreateProductData, UpdateProductData, ApiResponse, PaginatedResponse } from '../models/types';

const products = new Hono();

// Get all products with filtering and pagination
products.get('/', optionalAuthMiddleware, async (c) => {
  try {
    const query = c.req.query();
    const validatedQuery = productQuerySchema.parse(query);

    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Add filters
    if (validatedQuery.category_id) {
      whereClause += ` AND p.category_id = $${paramIndex}`;
      queryParams.push(validatedQuery.category_id);
      paramIndex++;
    }

    if (validatedQuery.search) {
      whereClause += ` AND (p.name ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`;
      queryParams.push(`%${validatedQuery.search}%`);
      paramIndex++;
    }

    if (validatedQuery.min_price) {
      whereClause += ` AND p.price >= $${paramIndex}`;
      queryParams.push(validatedQuery.min_price);
      paramIndex++;
    }

    if (validatedQuery.max_price) {
      whereClause += ` AND p.price <= $${paramIndex}`;
      queryParams.push(validatedQuery.max_price);
      paramIndex++;
    }

    if (validatedQuery.is_active !== undefined) {
      whereClause += ` AND p.is_active = $${paramIndex}`;
      queryParams.push(validatedQuery.is_active);
      paramIndex++;
    }

    // Count total products
    const countResult = await pool.query(
      `SELECT COUNT(*) as total FROM products p ${whereClause}`,
      queryParams
    );
    const total = parseInt(countResult.rows[0].total);

    // Calculate pagination
    const offset = (validatedQuery.page - 1) * validatedQuery.limit;
    const totalPages = Math.ceil(total / validatedQuery.limit);

    // Get products with category information
    const result = await pool.query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       ${whereClause}
       ORDER BY p.created_at DESC 
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...queryParams, validatedQuery.limit, offset]
    );

    return c.json({
      success: true,
      data: result.rows,
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        total,
        totalPages
      }
    } as ApiResponse<PaginatedResponse<Product>>);

  } catch (error: any) {
    console.error('Get products error:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch products'
    } as ApiResponse, 500);
  }
});

// Get single product by ID
products.get('/:id', optionalAuthMiddleware, async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    if (isNaN(id)) {
      return c.json({
        success: false,
        error: 'Invalid product ID'
      } as ApiResponse, 400);
    }

    const result = await pool.query(
      `SELECT p.*, c.name as category_name 
       FROM products p 
       LEFT JOIN categories c ON p.category_id = c.id 
       WHERE p.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Product not found'
      } as ApiResponse, 404);
    }

    return c.json({
      success: true,
      data: result.rows[0]
    } as ApiResponse);

  } catch (error: any) {
    console.error('Get product error:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch product'
    } as ApiResponse, 500);
  }
});

// Create new product (admin only)
products.post('/', authMiddleware, adminMiddleware, async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = createProductSchema.parse(body) as CreateProductData;

    const result = await pool.query(
      `INSERT INTO products (name, description, price, stock_quantity, category_id, image_url)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [
        validatedData.name,
        validatedData.description,
        validatedData.price,
        validatedData.stock_quantity,
        validatedData.category_id,
        validatedData.image_url
      ]
    );

    return c.json({
      success: true,
      data: result.rows[0]
    } as ApiResponse, 201);

  } catch (error: any) {
    console.error('Create product error:', error);
    return c.json({
      success: false,
      error: error.message || 'Failed to create product'
    } as ApiResponse, 400);
  }
});

// Update product (admin only)
products.put('/:id', authMiddleware, adminMiddleware, async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    if (isNaN(id)) {
      return c.json({
        success: false,
        error: 'Invalid product ID'
      } as ApiResponse, 400);
    }

    const body = await c.req.json();
    const validatedData = updateProductSchema.parse(body) as UpdateProductData;

    // Check if product exists
    const existingProduct = await pool.query('SELECT id FROM products WHERE id = $1', [id]);
    if (existingProduct.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Product not found'
      } as ApiResponse, 404);
    }

    // Build dynamic update query
    const updates: any = {};
    const allowedFields = ['name', 'description', 'price', 'stock_quantity', 'category_id', 'image_url', 'is_active'];

    for (const field of allowedFields) {
      if (validatedData[field as keyof UpdateProductData] !== undefined) {
        updates[field] = validatedData[field as keyof UpdateProductData];
      }
    }

    if (Object.keys(updates).length === 0) {
      return c.json({
        success: false,
        error: 'No valid fields to update'
      } as ApiResponse, 400);
    }

    const setClause = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [id, ...Object.values(updates)];

    const result = await pool.query(
      `UPDATE products SET ${setClause}, updated_at = CURRENT_TIMESTAMP
       WHERE id = $1
       RETURNING *`,
      values
    );

    return c.json({
      success: true,
      data: result.rows[0]
    } as ApiResponse);

  } catch (error: any) {
    console.error('Update product error:', error);
    return c.json({
      success: false,
      error: error.message || 'Failed to update product'
    } as ApiResponse, 400);
  }
});

// Delete product (admin only)
products.delete('/:id', authMiddleware, adminMiddleware, async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    if (isNaN(id)) {
      return c.json({
        success: false,
        error: 'Invalid product ID'
      } as ApiResponse, 400);
    }

    const result = await pool.query('DELETE FROM products WHERE id = $1 RETURNING id', [id]);

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Product not found'
      } as ApiResponse, 404);
    }

    return c.json({
      success: true,
      message: 'Product deleted successfully'
    } as ApiResponse);

  } catch (error: any) {
    console.error('Delete product error:', error);
    return c.json({
      success: false,
      error: 'Failed to delete product'
    } as ApiResponse, 500);
  }
});

export default products;
