import { Hono } from 'hono';
import bcrypt from 'bcryptjs';
import { pool } from '../db/config';
import { generateToken } from '../utils/jwt';
import { registerSchema, loginSchema } from '../utils/validation';
import { authMiddleware } from '../middleware/auth';
import { User, CreateUserData, LoginData, ApiResponse } from '../models/types';

const auth = new Hono();

// Register new user
auth.post('/register', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = registerSchema.parse(body) as CreateUserData;

    // Check if user already exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1',
      [validatedData.email]
    );

    if (existingUser.rows.length > 0) {
      return c.json({
        success: false,
        error: 'User with this email already exists'
      } as ApiResponse, 400);
    }

    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 10);

    // Create user
    const result = await pool.query(
      `INSERT INTO users (email, password_hash, first_name, last_name, phone)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id, email, first_name, last_name, phone, is_admin, created_at`,
      [validatedData.email, passwordHash, validatedData.first_name, validatedData.last_name, validatedData.phone]
    );

    const user = result.rows[0];

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      isAdmin: user.is_admin
    });

    return c.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          is_admin: user.is_admin,
          created_at: user.created_at
        },
        token
      }
    } as ApiResponse);

  } catch (error: any) {
    console.error('Registration error:', error);
    return c.json({
      success: false,
      error: error.message || 'Registration failed'
    } as ApiResponse, 400);
  }
});

// Login user
auth.post('/login', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = loginSchema.parse(body) as LoginData;

    // Find user by email
    const result = await pool.query(
      'SELECT * FROM users WHERE email = $1',
      [validatedData.email]
    );

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Invalid email or password'
      } as ApiResponse, 401);
    }

    const user: User = result.rows[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(validatedData.password, user.password_hash);
    if (!isValidPassword) {
      return c.json({
        success: false,
        error: 'Invalid email or password'
      } as ApiResponse, 401);
    }

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      isAdmin: user.is_admin
    });

    return c.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          is_admin: user.is_admin,
          created_at: user.created_at
        },
        token
      }
    } as ApiResponse);

  } catch (error: any) {
    console.error('Login error:', error);
    return c.json({
      success: false,
      error: error.message || 'Login failed'
    } as ApiResponse, 400);
  }
});

// Get current user profile
auth.get('/me', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;

    const result = await pool.query(
      'SELECT id, email, first_name, last_name, phone, is_admin, created_at FROM users WHERE id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'User not found'
      } as ApiResponse, 404);
    }

    return c.json({
      success: true,
      data: result.rows[0]
    } as ApiResponse);

  } catch (error: any) {
    console.error('Get profile error:', error);
    return c.json({
      success: false,
      error: 'Failed to get user profile'
    } as ApiResponse, 500);
  }
});

// Update user profile
auth.put('/me', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;
    const body = await c.req.json();

    // Validate allowed fields
    const allowedFields = ['first_name', 'last_name', 'phone'];
    const updates: any = {};
    
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updates[field] = body[field];
      }
    }

    if (Object.keys(updates).length === 0) {
      return c.json({
        success: false,
        error: 'No valid fields to update'
      } as ApiResponse, 400);
    }

    // Build dynamic query
    const setClause = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [userId, ...Object.values(updates)];

    const result = await pool.query(
      `UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $1 
       RETURNING id, email, first_name, last_name, phone, is_admin, updated_at`,
      values
    );

    return c.json({
      success: true,
      data: result.rows[0]
    } as ApiResponse);

  } catch (error: any) {
    console.error('Update profile error:', error);
    return c.json({
      success: false,
      error: 'Failed to update profile'
    } as ApiResponse, 500);
  }
});

export default auth;
