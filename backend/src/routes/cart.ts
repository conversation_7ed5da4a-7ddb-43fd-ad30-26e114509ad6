import { Hono } from 'hono';
import { pool } from '../db/config';
import { authMiddleware } from '../middleware/auth';
import { addToCartSchema, updateCartItemSchema } from '../utils/validation';
import { CartItemWithProduct, AddToCartData, ApiResponse } from '../models/types';

const cart = new Hono();

// Get user's cart items
cart.get('/', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;

    const result = await pool.query(
      `SELECT 
        ci.id,
        ci.user_id,
        ci.product_id,
        ci.quantity,
        ci.created_at,
        ci.updated_at,
        p.name as product_name,
        p.price as product_price,
        p.image_url as product_image_url,
        (ci.quantity * p.price) as total_price
       FROM cart_items ci
       JOIN products p ON ci.product_id = p.id
       WHERE ci.user_id = $1 AND p.is_active = true
       ORDER BY ci.created_at DESC`,
      [userId]
    );

    const cartItems: CartItemWithProduct[] = result.rows;
    const totalAmount = cartItems.reduce((sum, item) => sum + item.total_price, 0);

    return c.json({
      success: true,
      data: {
        items: cartItems,
        total_amount: totalAmount,
        item_count: cartItems.length
      }
    } as ApiResponse);

  } catch (error: any) {
    console.error('Get cart error:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch cart items'
    } as ApiResponse, 500);
  }
});

// Add item to cart
cart.post('/items', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;
    const body = await c.req.json();
    const validatedData = addToCartSchema.parse(body) as AddToCartData;

    // Check if product exists and is active
    const productResult = await pool.query(
      'SELECT id, name, price, stock_quantity FROM products WHERE id = $1 AND is_active = true',
      [validatedData.product_id]
    );

    if (productResult.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Product not found or not available'
      } as ApiResponse, 404);
    }

    const product = productResult.rows[0];

    // Check if enough stock is available
    if (product.stock_quantity < validatedData.quantity) {
      return c.json({
        success: false,
        error: 'Insufficient stock available'
      } as ApiResponse, 400);
    }

    // Check if item already exists in cart
    const existingItem = await pool.query(
      'SELECT id, quantity FROM cart_items WHERE user_id = $1 AND product_id = $2',
      [userId, validatedData.product_id]
    );

    let result;
    if (existingItem.rows.length > 0) {
      // Update existing item
      const newQuantity = existingItem.rows[0].quantity + validatedData.quantity;
      
      if (newQuantity > product.stock_quantity) {
        return c.json({
          success: false,
          error: 'Total quantity would exceed available stock'
        } as ApiResponse, 400);
      }

      result = await pool.query(
        `UPDATE cart_items 
         SET quantity = $1, updated_at = CURRENT_TIMESTAMP 
         WHERE user_id = $2 AND product_id = $3 
         RETURNING *`,
        [newQuantity, userId, validatedData.product_id]
      );
    } else {
      // Create new cart item
      result = await pool.query(
        `INSERT INTO cart_items (user_id, product_id, quantity)
         VALUES ($1, $2, $3)
         RETURNING *`,
        [userId, validatedData.product_id, validatedData.quantity]
      );
    }

    return c.json({
      success: true,
      data: result.rows[0],
      message: 'Item added to cart successfully'
    } as ApiResponse, 201);

  } catch (error: any) {
    console.error('Add to cart error:', error);
    return c.json({
      success: false,
      error: error.message || 'Failed to add item to cart'
    } as ApiResponse, 400);
  }
});

// Update cart item quantity
cart.put('/items/:id', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;
    const itemId = parseInt(c.req.param('id'));
    
    if (isNaN(itemId)) {
      return c.json({
        success: false,
        error: 'Invalid cart item ID'
      } as ApiResponse, 400);
    }

    const body = await c.req.json();
    const validatedData = updateCartItemSchema.parse(body);

    // Check if cart item exists and belongs to user
    const cartItemResult = await pool.query(
      'SELECT ci.*, p.stock_quantity FROM cart_items ci JOIN products p ON ci.product_id = p.id WHERE ci.id = $1 AND ci.user_id = $2',
      [itemId, userId]
    );

    if (cartItemResult.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Cart item not found'
      } as ApiResponse, 404);
    }

    const cartItem = cartItemResult.rows[0];

    // Check stock availability
    if (validatedData.quantity > cartItem.stock_quantity) {
      return c.json({
        success: false,
        error: 'Insufficient stock available'
      } as ApiResponse, 400);
    }

    // Update cart item
    const result = await pool.query(
      `UPDATE cart_items 
       SET quantity = $1, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $2 AND user_id = $3 
       RETURNING *`,
      [validatedData.quantity, itemId, userId]
    );

    return c.json({
      success: true,
      data: result.rows[0],
      message: 'Cart item updated successfully'
    } as ApiResponse);

  } catch (error: any) {
    console.error('Update cart item error:', error);
    return c.json({
      success: false,
      error: error.message || 'Failed to update cart item'
    } as ApiResponse, 400);
  }
});

// Remove item from cart
cart.delete('/items/:id', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;
    const itemId = parseInt(c.req.param('id'));
    
    if (isNaN(itemId)) {
      return c.json({
        success: false,
        error: 'Invalid cart item ID'
      } as ApiResponse, 400);
    }

    const result = await pool.query(
      'DELETE FROM cart_items WHERE id = $1 AND user_id = $2 RETURNING id',
      [itemId, userId]
    );

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'Cart item not found'
      } as ApiResponse, 404);
    }

    return c.json({
      success: true,
      message: 'Item removed from cart successfully'
    } as ApiResponse);

  } catch (error: any) {
    console.error('Remove cart item error:', error);
    return c.json({
      success: false,
      error: 'Failed to remove cart item'
    } as ApiResponse, 500);
  }
});

// Clear entire cart
cart.delete('/', authMiddleware, async (c) => {
  try {
    const userId = c.user!.userId;

    await pool.query('DELETE FROM cart_items WHERE user_id = $1', [userId]);

    return c.json({
      success: true,
      message: 'Cart cleared successfully'
    } as ApiResponse);

  } catch (error: any) {
    console.error('Clear cart error:', error);
    return c.json({
      success: false,
      error: 'Failed to clear cart'
    } as ApiResponse, 500);
  }
});

export default cart;
