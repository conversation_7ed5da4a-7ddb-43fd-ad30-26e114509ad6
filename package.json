{"name": "ecommerce-platform", "version": "1.0.0", "description": "A complete e-commerce platform built with Hono, React, and PostgreSQL", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "bun run --parallel dev:backend dev:frontend", "dev:backend": "bun --cwd backend run dev", "dev:frontend": "bun --cwd frontend run dev", "build": "bun run build:backend && bun run build:frontend", "build:backend": "bun --cwd backend run build", "build:frontend": "bun --cwd frontend run build", "start": "bun run start:backend", "start:backend": "bun --cwd backend run start", "db:migrate": "bun --cwd backend run db:migrate", "db:seed": "bun --cwd backend run db:seed", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "test": "bun run test:backend && bun run test:frontend", "test:backend": "bun --cwd backend run test", "test:frontend": "bun --cwd frontend run test"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "author": "Your Name", "license": "MIT"}